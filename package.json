{"name": "react-vite-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.73.3", "@udecode/cn": "^46.0.9", "@udecode/plate": "^46.0.10", "@udecode/plate-basic-elements": "^47.0.0", "@udecode/plate-basic-marks": "^46.0.5", "@udecode/plate-block-quote": "^44.0.0", "@udecode/plate-heading": "^44.0.0", "@udecode/plate-image": "^16.0.1", "@udecode/plate-link": "^44.0.4", "@udecode/plate-list": "^44.0.5", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jotai": "^2.12.3", "lucide-react": "^0.484.0", "next-themes": "^0.4.6", "node-forge": "^1.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "react-simple-wysiwyg": "^3.2.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.23.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.0", "@types/node-forge": "^1.3.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.4.14", "typescript": "~5.8.2", "typescript-eslint": "^8.28.0", "vite": "^6.2.3", "vite-plugin-mkcert": "^1.17.8"}, "engines": {"node": ">=22.0.0"}}