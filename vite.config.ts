import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import mkcert from "vite-plugin-mkcert";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), mkcert()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  server: {
    https: {
      // Using default mkcert-generated certificate
    },
    proxy: {
      '/api/eldorado': {
        target: 'https://www.eldorado.gg',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/eldorado/, '/api'),
        configure: (proxy) => {
          proxy.on('proxyReq', (proxyReq) => {
            proxyReq.setHeader('Origin', 'https://www.eldorado.gg');
            proxyReq.setHeader('Referer', 'https://www.eldorado.gg/');
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
          });
        }
      }
    }
  },
});
