import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import mkcert from "vite-plugin-mkcert";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), mkcert()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  server: {
    https: {
      // Using default mkcert-generated certificate
    },
    proxy: {
      '/api/eldorado': {
        target: 'https://www.eldorado.gg',
        changeOrigin: true,
        secure: true,
        cookieDomainRewrite: false,
        cookiePathRewrite: false,
        rewrite: (path) => path.replace(/^\/api\/eldorado/, '/api'),
        configure: (proxy) => {
          proxy.on('proxyReq', (proxyReq, req) => {
            proxyReq.setHeader('Origin', 'https://www.eldorado.gg');
            proxyReq.setHeader('Referer', 'https://www.eldorado.gg/');
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

            // Forward cookies from the original request
            if (req.headers.cookie) {
              proxyReq.setHeader('Cookie', req.headers.cookie);
            }
          });

          proxy.on('proxyRes', (proxyRes, _req, res) => {
            // Don't rewrite Set-Cookie headers to preserve original domain
            const setCookieHeaders = proxyRes.headers['set-cookie'];
            if (setCookieHeaders) {
              // Keep the original Set-Cookie headers without domain rewriting
              res.setHeader('Set-Cookie', setCookieHeaders);
            }
          });
        }
      }
    }
  },
});
